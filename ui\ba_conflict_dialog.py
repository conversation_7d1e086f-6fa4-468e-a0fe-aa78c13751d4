"""
BA Number Conflict Resolution Dialog for PROJECT-ALPHA Enhanced Excel Import

This dialog handles BA number conflicts during Excel import by providing
side-by-side comparison of conflicting records with clear Accept/Skip options.
Maintains simple modal dialog approach as specified in requirements.

Key Features:
- Side-by-side comparison of existing vs. import records
- Clear highlighting of differences between records
- Simple Accept/Skip decision options
- Modal dialog in main GUI thread
- Equipment-centric conflict resolution
"""

import logging
from typing import Dict, List, Optional, Tuple
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QTableWidget, QTableWidgetItem, QHeaderView, QSplitter, QTextEdit,
    QGroupBox, QScrollArea, QWidget, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

from enhanced_import_preview_engine import EquipmentPreview
from models import Equipment
import config

logger = logging.getLogger('ba_conflict_dialog')

class BAConflictDialog(QDialog):
    """
    Dialog for resolving BA number conflicts during Excel import.
    
    Provides side-by-side comparison of existing database record vs.
    incoming import record with clear Accept/Skip options.
    """
    
    # Signals for conflict resolution
    conflict_resolved = pyqtSignal(str, bool)  # ba_number, accept_import
    
    def __init__(self, ba_number: str, existing_equipment: Equipment, 
                 import_equipment: EquipmentPreview, parent=None):
        """
        Initialize the BA conflict resolution dialog.
        
        Args:
            ba_number: The conflicting BA number
            existing_equipment: Equipment record from database
            import_equipment: Equipment record from Excel import
            parent: Parent widget
        """
        super().__init__(parent)
        self.ba_number = ba_number
        self.existing_equipment = existing_equipment
        self.import_equipment = import_equipment
        self.resolution = None  # Will be True for Accept, False for Skip
        
        self.setup_dialog()
        self.populate_comparison_data()
        
        # Set as modal dialog
        self.setModal(True)
    
    def setup_dialog(self):
        """Set up the conflict resolution dialog UI."""
        self.setWindowTitle(f"BA Number Conflict - {self.ba_number}")
        self.setMinimumSize(900, 600)
        self.resize(1000, 700)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Header section
        header_frame = self.create_header_section()
        main_layout.addWidget(header_frame)
        
        # Comparison section
        comparison_frame = self.create_comparison_section()
        main_layout.addWidget(comparison_frame)
        
        # Decision section
        decision_frame = self.create_decision_section()
        main_layout.addWidget(decision_frame)
        
        # Apply styling
        self.apply_dialog_styling()
    
    def create_header_section(self) -> QFrame:
        """Create the header section with conflict information."""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setMaximumHeight(100)
        
        layout = QVBoxLayout(header_frame)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # Conflict title
        title_label = QLabel(f"<h3>BA Number Conflict: {self.ba_number}</h3>")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
        layout.addWidget(title_label)
        
        # Conflict description
        desc_text = ("This BA number already exists in the database. "
                    "Please review both records and choose whether to accept the import "
                    "record (which will overwrite the existing record) or skip it.")
        desc_label = QLabel(desc_text)
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(desc_label)
        
        return header_frame
    
    def create_comparison_section(self) -> QFrame:
        """Create the side-by-side comparison section."""
        comparison_frame = QFrame()
        comparison_frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QHBoxLayout(comparison_frame)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # Existing record section
        existing_group = QGroupBox("Existing Record (Database)")
        existing_group.setStyleSheet("QGroupBox { font-weight: bold; color: #1976d2; }")
        existing_layout = QVBoxLayout(existing_group)
        
        self.existing_table = QTableWidget()
        self.setup_comparison_table(self.existing_table)
        existing_layout.addWidget(self.existing_table)
        
        layout.addWidget(existing_group)
        
        # Import record section
        import_group = QGroupBox("Import Record (Excel)")
        import_group.setStyleSheet("QGroupBox { font-weight: bold; color: #388e3c; }")
        import_layout = QVBoxLayout(import_group)
        
        self.import_table = QTableWidget()
        self.setup_comparison_table(self.import_table)
        import_layout.addWidget(self.import_table)
        
        layout.addWidget(import_group)
        
        return comparison_frame
    
    def setup_comparison_table(self, table: QTableWidget):
        """Set up a comparison table for displaying record data."""
        # Define fields to compare
        fields = [
            "BA Number", "Serial Number", "Make and Type", "Units Held",
            "Vintage Years", "Meterage (KMs)", "Active Status"
        ]
        
        table.setColumnCount(2)
        table.setHorizontalHeaderLabels(["Field", "Value"])
        table.setRowCount(len(fields))
        
        # Set field names
        for row, field in enumerate(fields):
            field_item = QTableWidgetItem(field)
            field_item.setFont(QFont("Arial", 9, QFont.Bold))
            field_item.setBackground(QColor(245, 245, 245))
            table.setItem(row, 0, field_item)
        
        # Configure table appearance
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        table.setAlternatingRowColors(True)
        table.setSelectionMode(QTableWidget.NoSelection)
        table.verticalHeader().setVisible(False)
        table.setMaximumHeight(250)
    
    def populate_comparison_data(self):
        """Populate both comparison tables with record data."""
        # Populate existing record table
        existing_values = [
            self.existing_equipment.ba_number or "Not Set",
            self.existing_equipment.serial_number or "Not Set",
            self.existing_equipment.make_and_type or "Not Set",
            str(self.existing_equipment.units_held or 1),
            str(self.existing_equipment.vintage_years or 0.0),
            str(self.existing_equipment.meterage_kms or 0.0),
            "Yes" if self.existing_equipment.is_active else "No"
        ]
        
        for row, value in enumerate(existing_values):
            value_item = QTableWidgetItem(str(value))
            self.existing_table.setItem(row, 1, value_item)
        
        # Populate import record table
        import_values = [
            self.import_equipment.ba_number or "Not Set",
            self.import_equipment.serial_number or "Not Set",
            self.import_equipment.make_and_type or "Not Set",
            str(self.import_equipment.units_held or 1),
            str(self.import_equipment.vintage_years or 0.0),
            str(self.import_equipment.meterage_kms or 0.0),
            "Yes" if self.import_equipment.is_active else "No"
        ]
        
        for row, value in enumerate(import_values):
            value_item = QTableWidgetItem(str(value))
            self.import_table.setItem(row, 1, value_item)
        
        # Highlight differences
        self.highlight_differences(existing_values, import_values)
    
    def highlight_differences(self, existing_values: List[str], import_values: List[str]):
        """Highlight differences between existing and import records."""
        for row, (existing_val, import_val) in enumerate(zip(existing_values, import_values)):
            if existing_val != import_val:
                # Highlight existing value in light red
                existing_item = self.existing_table.item(row, 1)
                if existing_item:
                    existing_item.setBackground(QColor(255, 235, 235))
                    existing_item.setFont(QFont("Arial", 9, QFont.Bold))
                
                # Highlight import value in light green
                import_item = self.import_table.item(row, 1)
                if import_item:
                    import_item.setBackground(QColor(235, 255, 235))
                    import_item.setFont(QFont("Arial", 9, QFont.Bold))
    
    def create_decision_section(self) -> QFrame:
        """Create the decision buttons section."""
        decision_frame = QFrame()
        decision_frame.setFrameStyle(QFrame.StyledPanel)
        decision_frame.setMaximumHeight(120)
        
        layout = QVBoxLayout(decision_frame)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # Decision instruction
        instruction_label = QLabel(
            "<b>Choose your action:</b><br>"
            "• <b>Accept Import:</b> Replace the existing record with the import record<br>"
            "• <b>Skip Import:</b> Keep the existing record and ignore the import record"
        )
        instruction_label.setWordWrap(True)
        instruction_label.setStyleSheet("color: #333; font-size: 11px;")
        layout.addWidget(instruction_label)
        
        # Decision buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.skip_btn = QPushButton("Skip Import")
        self.skip_btn.clicked.connect(self.skip_import)
        self.skip_btn.setMinimumSize(120, 35)
        self.skip_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        button_layout.addWidget(self.skip_btn)
        
        button_layout.addSpacing(20)
        
        self.accept_btn = QPushButton("Accept Import")
        self.accept_btn.clicked.connect(self.accept_import)
        self.accept_btn.setMinimumSize(120, 35)
        self.accept_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #388e3c;
            }
        """)
        button_layout.addWidget(self.accept_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return decision_frame
    
    def accept_import(self):
        """Handle accept import decision."""
        self.resolution = True
        self.conflict_resolved.emit(self.ba_number, True)
        self.accept()
    
    def skip_import(self):
        """Handle skip import decision."""
        self.resolution = False
        self.conflict_resolved.emit(self.ba_number, False)
        self.accept()
    
    def apply_dialog_styling(self):
        """Apply consistent styling to the dialog."""
        self.setStyleSheet("""
            QDialog {
                background-color: #fafafa;
            }
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
            }
            QGroupBox {
                font-size: 12px;
                font-weight: bold;
                padding-top: 10px;
                margin-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                border: 1px solid #d0d0d0;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 6px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)
    
    def closeEvent(self, event):
        """Handle dialog close event."""
        if self.resolution is None:
            # User closed dialog without making a decision - treat as skip
            self.resolution = False
            self.conflict_resolved.emit(self.ba_number, False)
        event.accept()
