"""
Import Preview Dialog for PROJECT-ALPHA Enhanced Excel Import

This dialog displays all imported Excel data in a user-friendly preview window
before any database modifications are made. Users can review, validate, and
selectively import records with full control over the import process.

Key Features:
- Tabbed interface for different entity types (Equipment, Fluids, Maintenance)
- BA numbers prominently displayed as primary identifiers
- Validation status indicators with clear visual feedback
- Individual record selection controls
- Bulk operation support (Accept All/Skip All)
- Column headers matching database schema
"""

import logging
from typing import Dict, List, Optional, Set
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QTableWidget, QTableWidgetItem,
    QHeaderView, QPushButton, QLabel, QCheckBox, QGroupBox, QSplitter,
    QTextEdit, QProgressBar, QMessageBox, QFrame, QScrollArea, QWidget,
    QButtonGroup, QRadioButton
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon

from enhanced_import_preview_engine import ImportPreviewData, EquipmentPreview, FluidPreview, MaintenancePreview

logger = logging.getLogger('import_preview_dialog')

class ImportPreviewDialog(QDialog):
    """
    Dialog for previewing Excel import data before database modifications.
    
    Provides comprehensive preview with validation status, user controls,
    and confirmation options as specified in requirements.
    """
    
    # Signals for communication with parent
    import_confirmed = pyqtSignal(dict)  # User selections for import
    import_cancelled = pyqtSignal()
    
    def __init__(self, preview_data: ImportPreviewData, parent=None):
        """
        Initialize the import preview dialog.
        
        Args:
            preview_data: Complete preview data from the preview engine
            parent: Parent widget
        """
        super().__init__(parent)
        self.preview_data = preview_data
        self.user_selections = {
            'equipment': {},  # row_index -> accept/reject
            'fluids': {},
            'maintenance': {}
        }
        
        self.setup_dialog()
        self.populate_preview_data()
        
        # Set dialog as modal to maintain single-window architecture
        self.setModal(True)
    
    def setup_dialog(self):
        """Set up the dialog UI components."""
        self.setWindowTitle("Excel Import Preview - Review Data Before Import")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # Header section with file info and validation summary
        header_frame = self.create_header_section()
        main_layout.addWidget(header_frame)
        
        # Main content area with tabs
        content_splitter = QSplitter(Qt.Vertical)
        
        # Tabbed preview area
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        content_splitter.addWidget(self.tab_widget)
        
        # Create tabs for different entity types
        self.equipment_tab = self.create_equipment_tab()
        self.fluids_tab = self.create_fluids_tab()
        self.maintenance_tab = self.create_maintenance_tab()
        
        self.tab_widget.addTab(self.equipment_tab, "Equipment")
        self.tab_widget.addTab(self.fluids_tab, "Fluids")
        self.tab_widget.addTab(self.maintenance_tab, "Maintenance")
        
        # Validation details area (collapsible)
        validation_frame = self.create_validation_details_section()
        content_splitter.addWidget(validation_frame)
        
        # Set splitter proportions
        content_splitter.setSizes([600, 200])
        main_layout.addWidget(content_splitter)
        
        # Control buttons section
        controls_frame = self.create_controls_section()
        main_layout.addWidget(controls_frame)
        
        # Apply styling
        self.apply_dialog_styling()
    
    def create_header_section(self) -> QFrame:
        """Create the header section with file info and validation summary."""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setMaximumHeight(120)
        
        layout = QVBoxLayout(header_frame)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # File information
        if self.preview_data.file_info:
            file_info = self.preview_data.file_info
            file_label = QLabel(f"<b>File:</b> {file_info.file_name} "
                               f"({file_info.file_size // 1024} KB, {len(file_info.sheet_names)} sheets)")
            file_label.setFont(QFont("Arial", 10))
            layout.addWidget(file_label)
        
        # Validation summary
        summary = self.preview_data.validation_summary
        if summary:
            summary_text = (f"<b>Records:</b> {summary.get('total_records', 0)} total, "
                           f"{summary.get('valid_records', 0)} valid, "
                           f"{summary.get('records_with_warnings', 0)} with warnings, "
                           f"{summary.get('records_with_errors', 0)} with errors")
            
            if summary.get('ba_number_conflicts', 0) > 0:
                summary_text += f" | <b style='color: red;'>BA Conflicts:</b> {summary['ba_number_conflicts']}"
            
            summary_label = QLabel(summary_text)
            summary_label.setFont(QFont("Arial", 10))
            layout.addWidget(summary_label)
        
        # Processing errors
        if self.preview_data.processing_errors:
            error_text = f"<b style='color: red;'>Processing Errors:</b> {len(self.preview_data.processing_errors)}"
            error_label = QLabel(error_text)
            error_label.setFont(QFont("Arial", 10))
            layout.addWidget(error_label)
        
        return header_frame
    
    def create_equipment_tab(self) -> QWidget:
        """Create the equipment preview tab."""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # Bulk controls for equipment
        bulk_controls = self.create_bulk_controls("equipment")
        layout.addWidget(bulk_controls)
        
        # Equipment table
        self.equipment_table = QTableWidget()
        self.setup_equipment_table()
        layout.addWidget(self.equipment_table)
        
        return tab_widget
    
    def create_fluids_tab(self) -> QWidget:
        """Create the fluids preview tab."""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # Bulk controls for fluids
        bulk_controls = self.create_bulk_controls("fluids")
        layout.addWidget(bulk_controls)
        
        # Fluids table
        self.fluids_table = QTableWidget()
        self.setup_fluids_table()
        layout.addWidget(self.fluids_table)
        
        return tab_widget
    
    def create_maintenance_tab(self) -> QWidget:
        """Create the maintenance preview tab."""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # Bulk controls for maintenance
        bulk_controls = self.create_bulk_controls("maintenance")
        layout.addWidget(bulk_controls)
        
        # Maintenance table
        self.maintenance_table = QTableWidget()
        self.setup_maintenance_table()
        layout.addWidget(self.maintenance_table)
        
        return tab_widget
    
    def create_bulk_controls(self, entity_type: str) -> QFrame:
        """Create bulk control buttons for an entity type."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setMaximumHeight(60)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Entity type label
        type_label = QLabel(f"<b>{entity_type.title()} Records:</b>")
        layout.addWidget(type_label)
        
        layout.addStretch()
        
        # Bulk operation buttons
        accept_all_btn = QPushButton("Accept All")
        accept_all_btn.clicked.connect(lambda: self.bulk_operation(entity_type, True))
        accept_all_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        layout.addWidget(accept_all_btn)
        
        skip_all_btn = QPushButton("Skip All")
        skip_all_btn.clicked.connect(lambda: self.bulk_operation(entity_type, False))
        skip_all_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        layout.addWidget(skip_all_btn)
        
        return frame
    
    def create_validation_details_section(self) -> QFrame:
        """Create the validation details section."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Section header
        header_label = QLabel("<b>Validation Details</b>")
        layout.addWidget(header_label)
        
        # Validation details text area
        self.validation_details = QTextEdit()
        self.validation_details.setMaximumHeight(150)
        self.validation_details.setReadOnly(True)
        layout.addWidget(self.validation_details)
        
        return frame
    
    def create_controls_section(self) -> QFrame:
        """Create the dialog control buttons."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setMaximumHeight(70)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # Import statistics
        self.stats_label = QLabel()
        self.update_import_statistics()
        layout.addWidget(self.stats_label)
        
        layout.addStretch()
        
        # Control buttons
        self.cancel_btn = QPushButton("Cancel Import")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setMinimumWidth(120)
        layout.addWidget(self.cancel_btn)
        
        self.confirm_btn = QPushButton("Confirm Import")
        self.confirm_btn.clicked.connect(self.confirm_import)
        self.confirm_btn.setMinimumWidth(120)
        self.confirm_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        layout.addWidget(self.confirm_btn)
        
        return frame

    def setup_equipment_table(self):
        """Set up the equipment preview table."""
        # Define columns matching database schema with BA number prominent
        columns = [
            "Accept", "Status", "BA Number", "Serial Number", "Make and Type",
            "Units Held", "Vintage Years", "Meterage (KMs)", "Active", "Validation"
        ]

        self.equipment_table.setColumnCount(len(columns))
        self.equipment_table.setHorizontalHeaderLabels(columns)

        # Set column widths
        header = self.equipment_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # Accept checkbox
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # Status icon
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # BA Number (prominent)
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # Serial Number
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # Make and Type
        header.setSectionResizeMode(9, QHeaderView.Stretch)  # Validation

        self.equipment_table.setColumnWidth(0, 60)   # Accept
        self.equipment_table.setColumnWidth(1, 60)   # Status
        self.equipment_table.setColumnWidth(5, 80)   # Units Held
        self.equipment_table.setColumnWidth(6, 100)  # Vintage Years
        self.equipment_table.setColumnWidth(7, 120)  # Meterage
        self.equipment_table.setColumnWidth(8, 60)   # Active

        # Enable sorting and selection
        self.equipment_table.setSortingEnabled(True)
        self.equipment_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.equipment_table.setAlternatingRowColors(True)

    def setup_fluids_table(self):
        """Set up the fluids preview table."""
        columns = [
            "Accept", "Status", "BA Number", "Fluid Type", "Capacity (Ltrs)",
            "Current Stock (Ltrs)", "Validation"
        ]

        self.fluids_table.setColumnCount(len(columns))
        self.fluids_table.setHorizontalHeaderLabels(columns)

        # Set column properties
        header = self.fluids_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # Accept
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # Status
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # BA Number
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # Fluid Type
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # Validation

        self.fluids_table.setColumnWidth(0, 60)
        self.fluids_table.setColumnWidth(1, 60)
        self.fluids_table.setColumnWidth(4, 120)
        self.fluids_table.setColumnWidth(5, 140)

        self.fluids_table.setSortingEnabled(True)
        self.fluids_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.fluids_table.setAlternatingRowColors(True)

    def setup_maintenance_table(self):
        """Set up the maintenance preview table."""
        columns = [
            "Accept", "Status", "BA Number", "Maintenance Type", "Category",
            "Done Date", "Next Due Date", "Validation"
        ]

        self.maintenance_table.setColumnCount(len(columns))
        self.maintenance_table.setHorizontalHeaderLabels(columns)

        # Set column properties
        header = self.maintenance_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # Accept
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # Status
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # BA Number
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # Maintenance Type
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # Validation

        self.maintenance_table.setColumnWidth(0, 60)
        self.maintenance_table.setColumnWidth(1, 60)
        self.maintenance_table.setColumnWidth(4, 100)
        self.maintenance_table.setColumnWidth(5, 100)
        self.maintenance_table.setColumnWidth(6, 100)

        self.maintenance_table.setSortingEnabled(True)
        self.maintenance_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.maintenance_table.setAlternatingRowColors(True)

    def populate_preview_data(self):
        """Populate all tables with preview data."""
        self.populate_equipment_data()
        self.populate_fluids_data()
        self.populate_maintenance_data()
        self.update_validation_details()
        self.update_import_statistics()

    def populate_equipment_data(self):
        """Populate the equipment table with preview data."""
        equipment_records = self.preview_data.equipment_records
        self.equipment_table.setRowCount(len(equipment_records))

        for row, equipment in enumerate(equipment_records):
            # Accept checkbox
            accept_checkbox = QCheckBox()
            accept_checkbox.setChecked(equipment.validation.is_valid)  # Auto-check valid records
            accept_checkbox.stateChanged.connect(
                lambda state, r=row: self.on_equipment_selection_changed(r, state == Qt.Checked)
            )
            self.equipment_table.setCellWidget(row, 0, accept_checkbox)

            # Status icon
            status_item = self.create_status_item(equipment.validation)
            self.equipment_table.setItem(row, 1, status_item)

            # BA Number (prominent display)
            ba_item = QTableWidgetItem(equipment.ba_number or "Not Assigned")
            if equipment.ba_number:
                ba_item.setFont(QFont("Arial", 10, QFont.Bold))
                if equipment.validation.ba_number_status == "duplicate":
                    ba_item.setBackground(QColor(255, 235, 235))  # Light red for conflicts
                elif equipment.validation.ba_number_status == "valid":
                    ba_item.setBackground(QColor(235, 255, 235))  # Light green for valid
            self.equipment_table.setItem(row, 2, ba_item)

            # Other equipment fields
            self.equipment_table.setItem(row, 3, QTableWidgetItem(equipment.serial_number or ""))
            self.equipment_table.setItem(row, 4, QTableWidgetItem(equipment.make_and_type or ""))
            self.equipment_table.setItem(row, 5, QTableWidgetItem(str(equipment.units_held or 1)))
            self.equipment_table.setItem(row, 6, QTableWidgetItem(str(equipment.vintage_years or 0.0)))
            self.equipment_table.setItem(row, 7, QTableWidgetItem(str(equipment.meterage_kms or 0.0)))
            self.equipment_table.setItem(row, 8, QTableWidgetItem("Yes" if equipment.is_active else "No"))

            # Validation summary
            validation_item = self.create_validation_summary_item(equipment.validation)
            self.equipment_table.setItem(row, 9, validation_item)

            # Initialize user selection
            self.user_selections['equipment'][row] = equipment.validation.is_valid

    def populate_fluids_data(self):
        """Populate the fluids table with preview data."""
        fluid_records = self.preview_data.fluid_records
        self.fluids_table.setRowCount(len(fluid_records))

        for row, fluid in enumerate(fluid_records):
            # Accept checkbox
            accept_checkbox = QCheckBox()
            accept_checkbox.setChecked(fluid.validation.is_valid)
            accept_checkbox.stateChanged.connect(
                lambda state, r=row: self.on_fluids_selection_changed(r, state == Qt.Checked)
            )
            self.fluids_table.setCellWidget(row, 0, accept_checkbox)

            # Status and data
            self.fluids_table.setItem(row, 1, self.create_status_item(fluid.validation))

            ba_item = QTableWidgetItem(fluid.ba_number or "Not Assigned")
            if fluid.ba_number:
                ba_item.setFont(QFont("Arial", 10, QFont.Bold))
            self.fluids_table.setItem(row, 2, ba_item)

            self.fluids_table.setItem(row, 3, QTableWidgetItem(fluid.fluid_type or ""))
            self.fluids_table.setItem(row, 4, QTableWidgetItem(str(fluid.capacity_ltrs or 0.0)))
            self.fluids_table.setItem(row, 5, QTableWidgetItem(str(fluid.current_stock_ltrs or 0.0)))
            self.fluids_table.setItem(row, 6, self.create_validation_summary_item(fluid.validation))

            self.user_selections['fluids'][row] = fluid.validation.is_valid

    def populate_maintenance_data(self):
        """Populate the maintenance table with preview data."""
        maintenance_records = self.preview_data.maintenance_records
        self.maintenance_table.setRowCount(len(maintenance_records))

        for row, maintenance in enumerate(maintenance_records):
            # Accept checkbox
            accept_checkbox = QCheckBox()
            accept_checkbox.setChecked(maintenance.validation.is_valid)
            accept_checkbox.stateChanged.connect(
                lambda state, r=row: self.on_maintenance_selection_changed(r, state == Qt.Checked)
            )
            self.maintenance_table.setCellWidget(row, 0, accept_checkbox)

            # Status and data
            self.maintenance_table.setItem(row, 1, self.create_status_item(maintenance.validation))

            ba_item = QTableWidgetItem(maintenance.ba_number or "Not Assigned")
            if maintenance.ba_number:
                ba_item.setFont(QFont("Arial", 10, QFont.Bold))
            self.maintenance_table.setItem(row, 2, ba_item)

            self.maintenance_table.setItem(row, 3, QTableWidgetItem(maintenance.maintenance_type or ""))
            self.maintenance_table.setItem(row, 4, QTableWidgetItem(maintenance.maintenance_category or ""))
            self.maintenance_table.setItem(row, 5, QTableWidgetItem(maintenance.done_date or ""))
            self.maintenance_table.setItem(row, 6, QTableWidgetItem(maintenance.next_due_date or ""))
            self.maintenance_table.setItem(row, 7, self.create_validation_summary_item(maintenance.validation))

            self.user_selections['maintenance'][row] = maintenance.validation.is_valid

    def create_status_item(self, validation) -> QTableWidgetItem:
        """Create a status indicator item based on validation results."""
        if not validation.is_valid:
            item = QTableWidgetItem("❌")
            item.setToolTip("Record has validation errors")
            item.setBackground(QColor(255, 235, 235))
        elif validation.warnings:
            item = QTableWidgetItem("⚠️")
            item.setToolTip("Record has warnings")
            item.setBackground(QColor(255, 248, 220))
        else:
            item = QTableWidgetItem("✅")
            item.setToolTip("Record is valid")
            item.setBackground(QColor(235, 255, 235))

        item.setTextAlignment(Qt.AlignCenter)
        return item

    def create_validation_summary_item(self, validation) -> QTableWidgetItem:
        """Create a validation summary item."""
        summary_parts = []

        if validation.errors:
            summary_parts.append(f"Errors: {len(validation.errors)}")

        if validation.warnings:
            summary_parts.append(f"Warnings: {len(validation.warnings)}")

        if not summary_parts:
            summary_parts.append("Valid")

        item = QTableWidgetItem(" | ".join(summary_parts))

        # Set tooltip with detailed messages
        tooltip_parts = []
        if validation.errors:
            tooltip_parts.append("ERRORS:")
            tooltip_parts.extend([f"• {error}" for error in validation.errors])

        if validation.warnings:
            tooltip_parts.append("WARNINGS:")
            tooltip_parts.extend([f"• {warning}" for warning in validation.warnings])

        if tooltip_parts:
            item.setToolTip("\n".join(tooltip_parts))

        return item

    def on_equipment_selection_changed(self, row: int, accepted: bool):
        """Handle equipment selection change."""
        self.user_selections['equipment'][row] = accepted
        self.update_import_statistics()

    def on_fluids_selection_changed(self, row: int, accepted: bool):
        """Handle fluids selection change."""
        self.user_selections['fluids'][row] = accepted
        self.update_import_statistics()

    def on_maintenance_selection_changed(self, row: int, accepted: bool):
        """Handle maintenance selection change."""
        self.user_selections['maintenance'][row] = accepted
        self.update_import_statistics()

    def bulk_operation(self, entity_type: str, accept_all: bool):
        """Perform bulk accept/skip operation."""
        if entity_type == 'equipment':
            table = self.equipment_table
            records = self.preview_data.equipment_records
        elif entity_type == 'fluids':
            table = self.fluids_table
            records = self.preview_data.fluid_records
        else:  # maintenance
            table = self.maintenance_table
            records = self.preview_data.maintenance_records

        # Update all checkboxes and selections
        for row in range(len(records)):
            checkbox = table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(accept_all)
                self.user_selections[entity_type][row] = accept_all

        self.update_import_statistics()

    def update_import_statistics(self):
        """Update the import statistics display."""
        total_selected = 0
        total_available = 0

        for entity_type, selections in self.user_selections.items():
            total_available += len(selections)
            total_selected += sum(1 for selected in selections.values() if selected)

        stats_text = f"Selected for import: {total_selected} of {total_available} records"
        if hasattr(self, 'stats_label'):
            self.stats_label.setText(stats_text)

    def update_validation_details(self):
        """Update the validation details text area."""
        details = []

        # Add processing errors
        if self.preview_data.processing_errors:
            details.append("PROCESSING ERRORS:")
            for error in self.preview_data.processing_errors:
                details.append(f"• {error}")
            details.append("")

        # Add BA number conflicts
        if self.preview_data.ba_number_conflicts:
            details.append("BA NUMBER CONFLICTS:")
            for ba_number in self.preview_data.ba_number_conflicts:
                details.append(f"• {ba_number} appears multiple times in import data")
            details.append("")

        # Add validation summary
        if self.preview_data.validation_summary:
            summary = self.preview_data.validation_summary
            details.append("VALIDATION SUMMARY:")
            details.append(f"• Total records: {summary.get('total_records', 0)}")
            details.append(f"• Valid records: {summary.get('valid_records', 0)}")
            details.append(f"• Records with warnings: {summary.get('records_with_warnings', 0)}")
            details.append(f"• Records with errors: {summary.get('records_with_errors', 0)}")
            details.append(f"• Missing BA numbers: {summary.get('missing_ba_numbers', 0)}")
            details.append(f"• Duplicate BA numbers: {summary.get('duplicate_ba_numbers', 0)}")

        if hasattr(self, 'validation_details'):
            self.validation_details.setPlainText("\n".join(details))

    def apply_dialog_styling(self):
        """Apply consistent styling to the dialog."""
        # Set dialog background
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2196F3;
            }
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)

    def confirm_import(self):
        """Handle import confirmation."""
        # Prepare import data based on user selections
        import_data = {
            'equipment': [],
            'fluids': [],
            'maintenance': [],
            'selections': self.user_selections
        }

        # Add selected equipment records
        for row, selected in self.user_selections['equipment'].items():
            if selected and row < len(self.preview_data.equipment_records):
                import_data['equipment'].append(self.preview_data.equipment_records[row])

        # Add selected fluid records
        for row, selected in self.user_selections['fluids'].items():
            if selected and row < len(self.preview_data.fluid_records):
                import_data['fluids'].append(self.preview_data.fluid_records[row])

        # Add selected maintenance records
        for row, selected in self.user_selections['maintenance'].items():
            if selected and row < len(self.preview_data.maintenance_records):
                import_data['maintenance'].append(self.preview_data.maintenance_records[row])

        # Emit confirmation signal
        self.import_confirmed.emit(import_data)
        self.accept()

    def reject(self):
        """Handle dialog cancellation."""
        self.import_cancelled.emit()
        super().reject()
