"""
Enhanced Import Controller for PROJECT-ALPHA Excel Import System

This controller orchestrates the complete enhanced Excel import workflow,
integrating all components: preview engine, conflict resolution, user controls,
and final database import. Maintains single-window architecture with modal dialogs.

Key Features:
- Complete import workflow orchestration
- Integration with existing robust importer
- Modal dialog management in main GUI thread
- Comprehensive error handling and user feedback
- Progress tracking and status updates
- User control and bulk operations support
"""

import logging
import os
from typing import Dict, List, Optional, Set, Tuple
from PyQt5.QtWidgets import QWidget, QMessageBox, QProgressDialog, QApplication
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QTimer

from enhanced_import_preview_engine import EnhancedImportPreviewEngine, ImportPreviewData
from ui.import_preview_dialog import ImportPreviewDialog
from ui.conflict_resolution_manager import ConflictResolutionManager
from robust_excel_importer_working import RobustExcelImporter
import config

logger = logging.getLogger('enhanced_import_controller')

class EnhancedImportController(QObject):
    """
    Controller for the complete enhanced Excel import workflow.
    
    Orchestrates the entire import process from file selection through
    final database import with comprehensive user control and feedback.
    """
    
    # Signals for workflow coordination
    import_started = pyqtSignal(str)  # file_path
    preview_ready = pyqtSignal(ImportPreviewData)
    conflicts_detected = pyqtSignal(int)  # conflict_count
    import_completed = pyqtSignal(dict)  # import_results
    import_failed = pyqtSignal(str)  # error_message
    import_cancelled = pyqtSignal()
    
    def __init__(self, parent: QWidget = None):
        """
        Initialize the enhanced import controller.
        
        Args:
            parent: Parent widget for modal dialogs
        """
        super().__init__(parent)
        self.parent_widget = parent
        
        # Core components
        self.preview_engine = None
        self.preview_dialog = None
        self.conflict_manager = None
        self.robust_importer = RobustExcelImporter()
        
        # Workflow state
        self.current_file_path = None
        self.preview_data = None
        self.user_selections = None
        self.conflict_resolutions = None
        self.import_in_progress = False
    
    def start_enhanced_import(self, file_path: str) -> bool:
        """
        Start the enhanced Excel import workflow.
        
        Args:
            file_path: Path to the Excel file to import
            
        Returns:
            True if import workflow started successfully, False otherwise
        """
        if self.import_in_progress:
            logger.warning("Import already in progress")
            return False
        
        if not os.path.exists(file_path):
            logger.error(f"Excel file not found: {file_path}")
            self._show_error("File not found", f"The selected Excel file could not be found:\n{file_path}")
            return False
        
        try:
            self.import_in_progress = True
            self.current_file_path = file_path
            
            logger.info(f"Starting enhanced Excel import: {file_path}")
            self.import_started.emit(file_path)
            
            # Step 1: Generate preview data
            self._generate_preview_data()
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting enhanced import: {e}")
            self._show_error("Import Error", f"Failed to start Excel import:\n{str(e)}")
            self._reset_workflow_state()
            return False
    
    def _generate_preview_data(self):
        """Generate preview data using the preview engine."""
        try:
            logger.info("Generating import preview data...")
            
            # Show progress dialog
            progress = QProgressDialog("Analyzing Excel file...", "Cancel", 0, 0, self.parent_widget)
            progress.setWindowTitle("Excel Import Analysis")
            progress.setModal(True)
            progress.show()
            
            # Process file to update progress
            QApplication.processEvents()
            
            # Create preview engine and generate data
            self.preview_engine = EnhancedImportPreviewEngine(self.current_file_path)
            self.preview_data = self.preview_engine.generate_preview()
            
            progress.close()
            
            if not self.preview_data:
                raise Exception("Failed to generate preview data")
            
            logger.info(f"Preview data generated: {len(self.preview_data.equipment_records)} equipment, "
                       f"{len(self.preview_data.fluid_records)} fluids, "
                       f"{len(self.preview_data.maintenance_records)} maintenance records")
            
            # Step 2: Show preview dialog
            self._show_preview_dialog()
            
        except Exception as e:
            logger.error(f"Error generating preview data: {e}")
            self._show_error("Preview Error", f"Failed to analyze Excel file:\n{str(e)}")
            self._reset_workflow_state()
    
    def _show_preview_dialog(self):
        """Show the import preview dialog."""
        try:
            logger.info("Showing import preview dialog")
            
            # Create and show preview dialog
            self.preview_dialog = ImportPreviewDialog(self.preview_data, self.parent_widget)
            
            # Connect dialog signals
            self.preview_dialog.import_confirmed.connect(self._on_preview_confirmed)
            self.preview_dialog.import_cancelled.connect(self._on_import_cancelled)
            
            # Show dialog
            self.preview_dialog.show()
            
            # Emit preview ready signal
            self.preview_ready.emit(self.preview_data)
            
        except Exception as e:
            logger.error(f"Error showing preview dialog: {e}")
            self._show_error("Dialog Error", f"Failed to show import preview:\n{str(e)}")
            self._reset_workflow_state()
    
    def _on_preview_confirmed(self, import_data: Dict):
        """
        Handle preview dialog confirmation.
        
        Args:
            import_data: User selections and data to import
        """
        try:
            logger.info("Import preview confirmed by user")
            
            # Store user selections
            self.user_selections = import_data
            
            # Close preview dialog
            if self.preview_dialog:
                self.preview_dialog.close()
                self.preview_dialog = None
            
            # Step 3: Check for BA number conflicts
            conflicting_ba_numbers = self._detect_ba_conflicts(import_data)
            
            if conflicting_ba_numbers:
                logger.info(f"Detected {len(conflicting_ba_numbers)} BA number conflicts")
                self.conflicts_detected.emit(len(conflicting_ba_numbers))
                self._resolve_conflicts(conflicting_ba_numbers)
            else:
                logger.info("No BA number conflicts detected")
                # Proceed directly to import
                self._perform_final_import()
            
        except Exception as e:
            logger.error(f"Error processing preview confirmation: {e}")
            self._show_error("Processing Error", f"Failed to process import selections:\n{str(e)}")
            self._reset_workflow_state()
    
    def _detect_ba_conflicts(self, import_data: Dict) -> Set[str]:
        """
        Detect BA number conflicts with existing database records.
        
        Args:
            import_data: Import data with user selections
            
        Returns:
            Set of conflicting BA numbers
        """
        conflicting_ba_numbers = set()
        
        try:
            # Get BA numbers from selected equipment records
            selected_ba_numbers = set()
            for equipment in import_data.get('equipment', []):
                if equipment.ba_number:
                    ba_number = equipment.ba_number.strip().upper()
                    selected_ba_numbers.add(ba_number)
            
            if not selected_ba_numbers:
                return conflicting_ba_numbers
            
            # Check against existing database records
            from database import get_db_connection
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Create placeholders for IN clause
            placeholders = ','.join(['?' for _ in selected_ba_numbers])
            query = f"SELECT UPPER(ba_number) FROM equipment WHERE UPPER(ba_number) IN ({placeholders})"
            
            cursor.execute(query, list(selected_ba_numbers))
            existing_ba_numbers = {row[0] for row in cursor.fetchall()}
            
            conn.close()
            
            # Find conflicts
            conflicting_ba_numbers = selected_ba_numbers.intersection(existing_ba_numbers)
            
            logger.info(f"BA conflict check: {len(selected_ba_numbers)} import BAs, "
                       f"{len(existing_ba_numbers)} existing BAs, "
                       f"{len(conflicting_ba_numbers)} conflicts")
            
        except Exception as e:
            logger.error(f"Error detecting BA conflicts: {e}")
        
        return conflicting_ba_numbers
    
    def _resolve_conflicts(self, conflicting_ba_numbers: Set[str]):
        """
        Start conflict resolution workflow.
        
        Args:
            conflicting_ba_numbers: Set of BA numbers with conflicts
        """
        try:
            logger.info(f"Starting conflict resolution for {len(conflicting_ba_numbers)} conflicts")
            
            # Create conflict resolution manager
            self.conflict_manager = ConflictResolutionManager(self.parent_widget)
            
            # Connect signals
            self.conflict_manager.resolution_completed.connect(self._on_conflicts_resolved)
            self.conflict_manager.resolution_cancelled.connect(self._on_import_cancelled)
            
            # Start conflict resolution
            success = self.conflict_manager.start_conflict_resolution(
                self.preview_data, conflicting_ba_numbers
            )
            
            if not success:
                logger.warning("Failed to start conflict resolution")
                # Proceed with import anyway (no valid conflicts found)
                self._perform_final_import()
            
        except Exception as e:
            logger.error(f"Error starting conflict resolution: {e}")
            self._show_error("Conflict Resolution Error", f"Failed to resolve BA conflicts:\n{str(e)}")
            self._reset_workflow_state()
    
    def _on_conflicts_resolved(self, conflict_decisions: Dict[str, bool]):
        """
        Handle completion of conflict resolution.
        
        Args:
            conflict_decisions: Dictionary mapping BA numbers to accept/reject decisions
        """
        try:
            logger.info(f"Conflicts resolved: {len(conflict_decisions)} decisions made")
            
            # Store conflict resolutions
            self.conflict_resolutions = conflict_decisions
            
            # Clean up conflict manager
            if self.conflict_manager:
                self.conflict_manager = None
            
            # Step 4: Perform final import
            self._perform_final_import()
            
        except Exception as e:
            logger.error(f"Error processing conflict resolutions: {e}")
            self._show_error("Resolution Error", f"Failed to process conflict resolutions:\n{str(e)}")
            self._reset_workflow_state()
    
    def _perform_final_import(self):
        """Perform the final database import."""
        try:
            logger.info("Starting final database import")
            
            # Show import progress dialog
            progress = QProgressDialog("Importing data to database...", "Cancel", 0, 100, self.parent_widget)
            progress.setWindowTitle("Database Import")
            progress.setModal(True)
            progress.show()
            
            # Prepare import data based on user selections and conflict resolutions
            final_import_data = self._prepare_final_import_data()
            
            # Update progress
            progress.setValue(25)
            QApplication.processEvents()
            
            # Perform database import using robust importer
            import_results = self._execute_database_import(final_import_data, progress)
            
            progress.close()
            
            # Step 5: Show completion results
            self._show_import_completion(import_results)
            
        except Exception as e:
            logger.error(f"Error performing final import: {e}")
            self._show_error("Import Error", f"Failed to import data to database:\n{str(e)}")
            self._reset_workflow_state()
    
    def _prepare_final_import_data(self) -> Dict:
        """Prepare final import data based on user selections and conflict resolutions."""
        final_data = {
            'equipment': [],
            'fluids': [],
            'maintenance': []
        }
        
        if not self.user_selections:
            return final_data
        
        # Filter equipment based on conflict resolutions
        for equipment in self.user_selections.get('equipment', []):
            if equipment.ba_number and self.conflict_resolutions:
                ba_number = equipment.ba_number.strip().upper()
                if ba_number in self.conflict_resolutions:
                    # Only include if user chose to accept the import
                    if self.conflict_resolutions[ba_number]:
                        final_data['equipment'].append(equipment)
                else:
                    # No conflict, include the record
                    final_data['equipment'].append(equipment)
            else:
                # No BA number or no conflicts, include the record
                final_data['equipment'].append(equipment)
        
        # Include all selected fluids and maintenance (they don't have BA conflicts)
        final_data['fluids'] = self.user_selections.get('fluids', [])
        final_data['maintenance'] = self.user_selections.get('maintenance', [])
        
        return final_data
    
    def _execute_database_import(self, import_data: Dict, progress: QProgressDialog) -> Dict:
        """Execute the actual database import."""
        results = {
            'equipment_imported': 0,
            'fluids_imported': 0,
            'maintenance_imported': 0,
            'errors': []
        }
        
        try:
            # Import equipment records
            progress.setLabelText("Importing equipment records...")
            progress.setValue(40)
            QApplication.processEvents()
            
            equipment_count = self.robust_importer.import_equipment_records(import_data.get('equipment', []))
            results['equipment_imported'] = equipment_count
            
            # Import fluid records
            progress.setLabelText("Importing fluid records...")
            progress.setValue(70)
            QApplication.processEvents()
            
            fluids_count = self.robust_importer.import_fluid_records(import_data.get('fluids', []))
            results['fluids_imported'] = fluids_count
            
            # Import maintenance records
            progress.setLabelText("Importing maintenance records...")
            progress.setValue(90)
            QApplication.processEvents()
            
            maintenance_count = self.robust_importer.import_maintenance_records(import_data.get('maintenance', []))
            results['maintenance_imported'] = maintenance_count
            
            progress.setValue(100)
            
        except Exception as e:
            results['errors'].append(str(e))
            logger.error(f"Database import error: {e}")
        
        return results

    def _show_import_completion(self, import_results: Dict):
        """Show import completion dialog with results."""
        try:
            total_imported = (import_results.get('equipment_imported', 0) +
                            import_results.get('fluids_imported', 0) +
                            import_results.get('maintenance_imported', 0))

            if import_results.get('errors'):
                # Show error dialog
                error_msg = f"Import completed with errors:\n\n"
                error_msg += f"Records imported: {total_imported}\n"
                error_msg += f"Equipment: {import_results.get('equipment_imported', 0)}\n"
                error_msg += f"Fluids: {import_results.get('fluids_imported', 0)}\n"
                error_msg += f"Maintenance: {import_results.get('maintenance_imported', 0)}\n\n"
                error_msg += "Errors encountered:\n"
                error_msg += "\n".join([f"• {error}" for error in import_results['errors']])

                QMessageBox.warning(self.parent_widget, "Import Completed with Errors", error_msg)
            else:
                # Show success dialog
                success_msg = f"Excel import completed successfully!\n\n"
                success_msg += f"Total records imported: {total_imported}\n"
                success_msg += f"• Equipment: {import_results.get('equipment_imported', 0)}\n"
                success_msg += f"• Fluids: {import_results.get('fluids_imported', 0)}\n"
                success_msg += f"• Maintenance: {import_results.get('maintenance_imported', 0)}\n\n"

                if self.conflict_resolutions:
                    conflict_summary = self._get_conflict_summary()
                    success_msg += f"BA number conflicts resolved:\n"
                    success_msg += f"• Accepted: {conflict_summary.get('accepted', 0)}\n"
                    success_msg += f"• Skipped: {conflict_summary.get('skipped', 0)}"

                QMessageBox.information(self.parent_widget, "Import Successful", success_msg)

            # Emit completion signal
            self.import_completed.emit(import_results)

            # Reset workflow state
            self._reset_workflow_state()

        except Exception as e:
            logger.error(f"Error showing import completion: {e}")
            self._reset_workflow_state()

    def _get_conflict_summary(self) -> Dict[str, int]:
        """Get summary of conflict resolutions."""
        if not self.conflict_resolutions:
            return {'accepted': 0, 'skipped': 0}

        accepted = sum(1 for decision in self.conflict_resolutions.values() if decision)
        total = len(self.conflict_resolutions)

        return {
            'accepted': accepted,
            'skipped': total - accepted
        }

    def _on_import_cancelled(self):
        """Handle import cancellation."""
        logger.info("Import cancelled by user")

        # Clean up any open dialogs
        if self.preview_dialog:
            self.preview_dialog.close()
            self.preview_dialog = None

        if self.conflict_manager:
            self.conflict_manager = None

        # Emit cancellation signal
        self.import_cancelled.emit()

        # Reset workflow state
        self._reset_workflow_state()

    def _show_error(self, title: str, message: str):
        """Show error message dialog."""
        if self.parent_widget:
            QMessageBox.critical(self.parent_widget, title, message)
        else:
            logger.error(f"{title}: {message}")

        # Emit error signal
        self.import_failed.emit(message)

    def _reset_workflow_state(self):
        """Reset the workflow state for next import."""
        self.current_file_path = None
        self.preview_data = None
        self.user_selections = None
        self.conflict_resolutions = None
        self.import_in_progress = False

        # Clean up components
        self.preview_engine = None
        if self.preview_dialog:
            self.preview_dialog.close()
            self.preview_dialog = None
        if self.conflict_manager:
            self.conflict_manager = None

        logger.info("Import workflow state reset")

    def is_import_in_progress(self) -> bool:
        """
        Check if an import is currently in progress.

        Returns:
            True if import is in progress, False otherwise
        """
        return self.import_in_progress

    def cancel_current_import(self):
        """Cancel the current import if one is in progress."""
        if self.import_in_progress:
            logger.info("Cancelling current import")
            self._on_import_cancelled()

    def get_import_status(self) -> Dict[str, any]:
        """
        Get the current import status.

        Returns:
            Dictionary with current import status information
        """
        return {
            'in_progress': self.import_in_progress,
            'current_file': self.current_file_path,
            'has_preview_data': self.preview_data is not None,
            'has_user_selections': self.user_selections is not None,
            'has_conflict_resolutions': self.conflict_resolutions is not None,
            'preview_dialog_open': self.preview_dialog is not None,
            'conflict_manager_active': self.conflict_manager is not None
        }
