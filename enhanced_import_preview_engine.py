"""
Enhanced Import Preview Engine for PROJECT-ALPHA

This module provides a robust data preview engine that reads Excel files,
validates data types, performs business rule validation, and prepares data
for user review without making any database modifications.

Key Features:
- Multi-sheet Excel reading with robust header detection
- Data type validation and business rule checking  
- BA number format validation and uniqueness checking
- Preparation of preview data structures for UI display
- No database modifications during preview phase
"""

import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, date
import os
import re
from pathlib import Path

# Import existing PROJECT-ALPHA modules
import config
import database
import utils
from robust_excel_importer_working import RobustExcelImporter

logger = logging.getLogger('enhanced_import_preview')

@dataclass
class ValidationResult:
    """Result of data validation for a single record."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    ba_number_status: str = "unknown"  # valid, invalid, duplicate, missing

@dataclass
class EquipmentPreview:
    """Preview data structure for equipment records."""
    row_index: int
    ba_number: Optional[str]
    serial_number: Optional[str]
    make_and_type: Optional[str]
    units_held: Optional[int]
    vintage_years: Optional[float]
    meterage_kms: Optional[float]
    is_active: bool = True
    validation: ValidationResult = field(default_factory=ValidationResult)
    raw_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class FluidPreview:
    """Preview data structure for fluid records."""
    row_index: int
    equipment_id: Optional[int]
    ba_number: Optional[str]  # For linking to equipment
    fluid_type: Optional[str]
    capacity_ltrs: Optional[float]
    current_stock_ltrs: Optional[float]
    validation: ValidationResult = field(default_factory=ValidationResult)
    raw_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MaintenancePreview:
    """Preview data structure for maintenance records."""
    row_index: int
    equipment_id: Optional[int]
    ba_number: Optional[str]  # For linking to equipment
    maintenance_type: Optional[str]
    maintenance_category: Optional[str]
    done_date: Optional[str]
    next_due_date: Optional[str]
    validation: ValidationResult = field(default_factory=ValidationResult)
    raw_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class FileInfo:
    """Information about the Excel file being imported."""
    file_path: str
    file_name: str
    file_size: int
    sheet_names: List[str]
    total_rows: int
    read_timestamp: datetime

@dataclass
class ImportPreviewData:
    """Complete preview data structure for Excel import."""
    equipment_records: List[EquipmentPreview] = field(default_factory=list)
    fluid_records: List[FluidPreview] = field(default_factory=list)
    maintenance_records: List[MaintenancePreview] = field(default_factory=list)
    file_info: Optional[FileInfo] = None
    validation_summary: Dict[str, int] = field(default_factory=dict)
    ba_number_conflicts: List[str] = field(default_factory=list)
    processing_errors: List[str] = field(default_factory=list)

class EnhancedImportPreviewEngine:
    """
    Enhanced import preview engine that reads and validates Excel data
    without making database modifications.
    """
    
    def __init__(self, file_path: str):
        """
        Initialize the preview engine.
        
        Args:
            file_path: Path to the Excel file to preview
        """
        self.file_path = file_path
        self.existing_ba_numbers = set()
        self.robust_importer = RobustExcelImporter()
        
        # Load existing BA numbers from database
        self._load_existing_ba_numbers()
    
    def _load_existing_ba_numbers(self):
        """Load existing BA numbers from the database for conflict detection."""
        try:
            query = "SELECT DISTINCT ba_number FROM equipment WHERE ba_number IS NOT NULL AND ba_number != ''"
            results = database.execute_query(query)
            if results:
                self.existing_ba_numbers = {row['ba_number'].strip().upper() for row in results if row['ba_number']}
            logger.info(f"Loaded {len(self.existing_ba_numbers)} existing BA numbers for conflict detection")
        except Exception as e:
            logger.error(f"Failed to load existing BA numbers: {e}")
            self.existing_ba_numbers = set()
    
    def read_and_validate_excel(self) -> ImportPreviewData:
        """
        Read Excel file and validate data without database modifications.
        
        Returns:
            ImportPreviewData: Complete preview data structure
        """
        logger.info(f"Starting Excel preview for file: {self.file_path}")
        
        preview_data = ImportPreviewData()
        
        try:
            # Validate file exists and is readable
            if not os.path.exists(self.file_path):
                preview_data.processing_errors.append(f"File not found: {self.file_path}")
                return preview_data
            
            # Get file information
            preview_data.file_info = self._get_file_info()
            
            # Read Excel file using robust importer's logic
            excel_data = self._read_excel_file()
            
            if not excel_data:
                preview_data.processing_errors.append("Failed to read Excel file or no data found")
                return preview_data
            
            # Process each sheet's data
            for sheet_name, df in excel_data.items():
                logger.info(f"Processing sheet: {sheet_name} with {len(df)} rows")
                
                # Determine sheet type and process accordingly
                sheet_type = self._determine_sheet_type(sheet_name, df)
                
                if sheet_type == 'equipment':
                    equipment_records = self._process_equipment_sheet(df, sheet_name)
                    preview_data.equipment_records.extend(equipment_records)
                elif sheet_type == 'fluids':
                    fluid_records = self._process_fluids_sheet(df, sheet_name)
                    preview_data.fluid_records.extend(fluid_records)
                elif sheet_type == 'maintenance':
                    maintenance_records = self._process_maintenance_sheet(df, sheet_name)
                    preview_data.maintenance_records.extend(maintenance_records)
                else:
                    logger.warning(f"Unknown sheet type for {sheet_name}, attempting equipment processing")
                    equipment_records = self._process_equipment_sheet(df, sheet_name)
                    preview_data.equipment_records.extend(equipment_records)
            
            # Validate BA numbers and detect conflicts
            self._validate_ba_numbers(preview_data)
            
            # Generate validation summary
            self._generate_validation_summary(preview_data)
            
            logger.info(f"Excel preview completed: {len(preview_data.equipment_records)} equipment, "
                       f"{len(preview_data.fluid_records)} fluids, {len(preview_data.maintenance_records)} maintenance")
            
        except Exception as e:
            error_msg = f"Error during Excel preview: {e}"
            logger.error(error_msg)
            preview_data.processing_errors.append(error_msg)
        
        return preview_data
    
    def _get_file_info(self) -> FileInfo:
        """Get information about the Excel file."""
        file_path = Path(self.file_path)
        
        try:
            # Get basic file info
            file_size = file_path.stat().st_size
            
            # Read Excel file to get sheet names and row count
            excel_file = pd.ExcelFile(self.file_path)
            sheet_names = excel_file.sheet_names
            
            # Count total rows across all sheets
            total_rows = 0
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
                    total_rows += len(df)
                except Exception:
                    pass  # Skip problematic sheets for row counting
            
            return FileInfo(
                file_path=str(file_path),
                file_name=file_path.name,
                file_size=file_size,
                sheet_names=sheet_names,
                total_rows=total_rows,
                read_timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error getting file info: {e}")
            return FileInfo(
                file_path=str(file_path),
                file_name=file_path.name,
                file_size=0,
                sheet_names=[],
                total_rows=0,
                read_timestamp=datetime.now()
            )

    def _read_excel_file(self) -> Dict[str, pd.DataFrame]:
        """
        Read Excel file using robust importer's logic.

        Returns:
            Dict mapping sheet names to DataFrames
        """
        excel_data = {}

        try:
            excel_file = pd.ExcelFile(self.file_path)

            for sheet_name in excel_file.sheet_names:
                try:
                    # Use robust importer's header detection logic
                    df = self.robust_importer._read_sheet_with_headers(excel_file, sheet_name)

                    if df is not None and not df.empty:
                        # Clean column names
                        df.columns = [str(col).strip() for col in df.columns]
                        excel_data[sheet_name] = df
                        logger.info(f"Successfully read sheet '{sheet_name}' with {len(df)} rows and {len(df.columns)} columns")
                    else:
                        logger.warning(f"Sheet '{sheet_name}' is empty or could not be read")

                except Exception as e:
                    logger.error(f"Error reading sheet '{sheet_name}': {e}")
                    continue

        except Exception as e:
            logger.error(f"Error reading Excel file: {e}")

        return excel_data

    def _determine_sheet_type(self, sheet_name: str, df: pd.DataFrame) -> str:
        """
        Determine the type of data in a sheet based on sheet name and columns.

        Args:
            sheet_name: Name of the Excel sheet
            df: DataFrame containing the sheet data

        Returns:
            Sheet type: 'equipment', 'fluids', 'maintenance', or 'unknown'
        """
        sheet_name_lower = sheet_name.lower()
        columns_lower = [col.lower() for col in df.columns]

        # Check for equipment indicators
        equipment_indicators = ['ba_number', 'ba number', 'serial_number', 'make_and_type', 'equipment']
        if any(indicator in sheet_name_lower for indicator in ['equipment', 'asset', 'inventory']):
            return 'equipment'
        if any(indicator in ' '.join(columns_lower) for indicator in equipment_indicators):
            return 'equipment'

        # Check for fluids indicators
        fluids_indicators = ['fluid', 'oil', 'lubricant', 'capacity', 'stock']
        if any(indicator in sheet_name_lower for indicator in fluids_indicators):
            return 'fluids'
        if any(indicator in ' '.join(columns_lower) for indicator in fluids_indicators):
            return 'fluids'

        # Check for maintenance indicators
        maintenance_indicators = ['maintenance', 'service', 'tm-', 'done_date', 'due_date']
        if any(indicator in sheet_name_lower for indicator in maintenance_indicators):
            return 'maintenance'
        if any(indicator in ' '.join(columns_lower) for indicator in maintenance_indicators):
            return 'maintenance'

        # Default to equipment if uncertain
        return 'equipment'

    def _process_equipment_sheet(self, df: pd.DataFrame, sheet_name: str) -> List[EquipmentPreview]:
        """
        Process equipment data from a DataFrame.

        Args:
            df: DataFrame containing equipment data
            sheet_name: Name of the source sheet

        Returns:
            List of EquipmentPreview objects
        """
        equipment_records = []

        # Map common column variations to standard field names
        column_mapping = self._get_equipment_column_mapping(df.columns)

        for index, row in df.iterrows():
            try:
                # Extract equipment data using column mapping
                equipment = EquipmentPreview(
                    row_index=index,
                    ba_number=self._extract_field(row, column_mapping.get('ba_number')),
                    serial_number=self._extract_field(row, column_mapping.get('serial_number')),
                    make_and_type=self._extract_field(row, column_mapping.get('make_and_type')),
                    units_held=self._extract_numeric_field(row, column_mapping.get('units_held'), default=1),
                    vintage_years=self._extract_numeric_field(row, column_mapping.get('vintage_years'), default=0.0),
                    meterage_kms=self._extract_numeric_field(row, column_mapping.get('meterage_kms'), default=0.0),
                    is_active=self._extract_boolean_field(row, column_mapping.get('is_active'), default=True),
                    raw_data=row.to_dict()
                )

                # Validate the equipment record
                equipment.validation = self._validate_equipment_record(equipment)

                equipment_records.append(equipment)

            except Exception as e:
                logger.error(f"Error processing equipment row {index} in sheet {sheet_name}: {e}")
                # Create a record with validation errors
                error_equipment = EquipmentPreview(
                    row_index=index,
                    ba_number=None,
                    serial_number=None,
                    make_and_type=None,
                    raw_data=row.to_dict() if hasattr(row, 'to_dict') else {}
                )
                error_equipment.validation = ValidationResult(
                    is_valid=False,
                    errors=[f"Error processing row: {str(e)}"]
                )
                equipment_records.append(error_equipment)

        return equipment_records

    def _get_equipment_column_mapping(self, columns: List[str]) -> Dict[str, str]:
        """
        Map Excel columns to equipment fields.

        Args:
            columns: List of column names from Excel

        Returns:
            Dictionary mapping field names to column names
        """
        mapping = {}
        columns_lower = {col.lower(): col for col in columns}

        # BA Number variations
        ba_variations = ['ba_number', 'ba number', 'ba_no', 'ba no', 'ba', 'asset_number', 'asset number']
        for variation in ba_variations:
            if variation in columns_lower:
                mapping['ba_number'] = columns_lower[variation]
                break

        # Serial Number variations
        serial_variations = ['serial_number', 'serial number', 'serial_no', 'serial no', 'serial']
        for variation in serial_variations:
            if variation in columns_lower:
                mapping['serial_number'] = columns_lower[variation]
                break

        # Make and Type variations
        make_variations = ['make_and_type', 'make and type', 'make_type', 'make type', 'equipment_type', 'type']
        for variation in make_variations:
            if variation in columns_lower:
                mapping['make_and_type'] = columns_lower[variation]
                break

        # Units Held variations
        units_variations = ['units_held', 'units held', 'units', 'quantity', 'qty']
        for variation in units_variations:
            if variation in columns_lower:
                mapping['units_held'] = columns_lower[variation]
                break

        # Vintage Years variations
        vintage_variations = ['vintage_years', 'vintage years', 'vintage', 'age_years', 'age years']
        for variation in vintage_variations:
            if variation in columns_lower:
                mapping['vintage_years'] = columns_lower[variation]
                break

        # Meterage variations
        meterage_variations = ['meterage_kms', 'meterage kms', 'meterage', 'mileage', 'kilometers', 'kms']
        for variation in meterage_variations:
            if variation in columns_lower:
                mapping['meterage_kms'] = columns_lower[variation]
                break

        # Active status variations
        active_variations = ['is_active', 'is active', 'active', 'status']
        for variation in active_variations:
            if variation in columns_lower:
                mapping['is_active'] = columns_lower[variation]
                break

        return mapping

    def _extract_field(self, row: pd.Series, column_name: Optional[str]) -> Optional[str]:
        """
        Extract a string field from a row.

        Args:
            row: Pandas Series representing a row
            column_name: Name of the column to extract

        Returns:
            String value or None if not found/empty
        """
        if not column_name or column_name not in row.index:
            return None

        value = row[column_name]
        if pd.isna(value) or str(value).strip() in ('', 'nan', 'NaN', 'NA', 'N/A', '-'):
            return None

        return str(value).strip()

    def _extract_numeric_field(self, row: pd.Series, column_name: Optional[str], default: float = 0.0) -> float:
        """
        Extract a numeric field from a row.

        Args:
            row: Pandas Series representing a row
            column_name: Name of the column to extract
            default: Default value if extraction fails

        Returns:
            Numeric value or default
        """
        if not column_name or column_name not in row.index:
            return default

        value = row[column_name]
        if pd.isna(value):
            return default

        try:
            # Handle string representations of numbers
            if isinstance(value, str):
                value = value.strip().replace(',', '')
                if value in ('', '-', 'NA', 'N/A'):
                    return default

            return float(value)
        except (ValueError, TypeError):
            return default

    def _extract_boolean_field(self, row: pd.Series, column_name: Optional[str], default: bool = True) -> bool:
        """
        Extract a boolean field from a row.

        Args:
            row: Pandas Series representing a row
            column_name: Name of the column to extract
            default: Default value if extraction fails

        Returns:
            Boolean value or default
        """
        if not column_name or column_name not in row.index:
            return default

        value = row[column_name]
        if pd.isna(value):
            return default

        # Convert various representations to boolean
        str_value = str(value).strip().lower()
        if str_value in ('true', '1', 'yes', 'active', 'y'):
            return True
        elif str_value in ('false', '0', 'no', 'inactive', 'n'):
            return False

        return default

    def _validate_equipment_record(self, equipment: EquipmentPreview) -> ValidationResult:
        """
        Validate an equipment record.

        Args:
            equipment: EquipmentPreview object to validate

        Returns:
            ValidationResult with validation status and messages
        """
        validation = ValidationResult(is_valid=True)

        # Validate BA Number
        if equipment.ba_number:
            ba_number_clean = equipment.ba_number.strip().upper()

            # Format validation
            is_valid_format, format_error = config.validate_ba_number(ba_number_clean)
            if not is_valid_format:
                validation.errors.append(f"Invalid BA number format: {format_error}")
                validation.ba_number_status = "invalid"
            else:
                # Check for duplicates
                if ba_number_clean in self.existing_ba_numbers:
                    validation.warnings.append(f"BA number {ba_number_clean} already exists in database")
                    validation.ba_number_status = "duplicate"
                else:
                    validation.ba_number_status = "valid"
        else:
            if config.REQUIRE_BA_NUMBER:
                validation.errors.append("BA number is required")
                validation.ba_number_status = "missing"
            else:
                validation.warnings.append("BA number not provided")
                validation.ba_number_status = "missing"

        # Validate Make and Type (required field)
        if not equipment.make_and_type:
            validation.errors.append("Make and Type is required")

        # Validate numeric fields
        if equipment.units_held is not None and equipment.units_held < 0:
            validation.errors.append("Units held cannot be negative")

        if equipment.vintage_years is not None and equipment.vintage_years < 0:
            validation.errors.append("Vintage years cannot be negative")

        if equipment.meterage_kms is not None and equipment.meterage_kms < 0:
            validation.errors.append("Meterage cannot be negative")

        # Set overall validation status
        validation.is_valid = len(validation.errors) == 0

        return validation

    def _process_fluids_sheet(self, df: pd.DataFrame, sheet_name: str) -> List[FluidPreview]:
        """
        Process fluids data from a DataFrame.

        Args:
            df: DataFrame containing fluids data
            sheet_name: Name of the source sheet

        Returns:
            List of FluidPreview objects
        """
        fluid_records = []

        # Map common column variations to standard field names
        column_mapping = self._get_fluids_column_mapping(df.columns)

        for index, row in df.iterrows():
            try:
                fluid = FluidPreview(
                    row_index=index,
                    ba_number=self._extract_field(row, column_mapping.get('ba_number')),
                    fluid_type=self._extract_field(row, column_mapping.get('fluid_type')),
                    capacity_ltrs=self._extract_numeric_field(row, column_mapping.get('capacity_ltrs')),
                    current_stock_ltrs=self._extract_numeric_field(row, column_mapping.get('current_stock_ltrs')),
                    raw_data=row.to_dict()
                )

                # Validate the fluid record
                fluid.validation = self._validate_fluid_record(fluid)

                fluid_records.append(fluid)

            except Exception as e:
                logger.error(f"Error processing fluid row {index} in sheet {sheet_name}: {e}")
                error_fluid = FluidPreview(
                    row_index=index,
                    ba_number=None,
                    fluid_type=None,
                    raw_data=row.to_dict() if hasattr(row, 'to_dict') else {}
                )
                error_fluid.validation = ValidationResult(
                    is_valid=False,
                    errors=[f"Error processing row: {str(e)}"]
                )
                fluid_records.append(error_fluid)

        return fluid_records

    def _get_fluids_column_mapping(self, columns: List[str]) -> Dict[str, str]:
        """Map Excel columns to fluid fields."""
        mapping = {}
        columns_lower = {col.lower(): col for col in columns}

        # BA Number variations (for linking to equipment)
        ba_variations = ['ba_number', 'ba number', 'ba_no', 'ba no', 'ba', 'asset_number', 'asset number']
        for variation in ba_variations:
            if variation in columns_lower:
                mapping['ba_number'] = columns_lower[variation]
                break

        # Fluid Type variations
        fluid_variations = ['fluid_type', 'fluid type', 'fluid', 'oil_type', 'oil type', 'lubricant_type']
        for variation in fluid_variations:
            if variation in columns_lower:
                mapping['fluid_type'] = columns_lower[variation]
                break

        # Capacity variations
        capacity_variations = ['capacity_ltrs', 'capacity ltrs', 'capacity', 'tank_capacity', 'max_capacity']
        for variation in capacity_variations:
            if variation in columns_lower:
                mapping['capacity_ltrs'] = columns_lower[variation]
                break

        # Current Stock variations
        stock_variations = ['current_stock_ltrs', 'current stock ltrs', 'current_stock', 'stock', 'available']
        for variation in stock_variations:
            if variation in columns_lower:
                mapping['current_stock_ltrs'] = columns_lower[variation]
                break

        return mapping

    def _validate_fluid_record(self, fluid: FluidPreview) -> ValidationResult:
        """Validate a fluid record."""
        validation = ValidationResult(is_valid=True)

        # Validate BA Number (for linking to equipment)
        if fluid.ba_number:
            ba_number_clean = fluid.ba_number.strip().upper()
            if ba_number_clean not in self.existing_ba_numbers:
                validation.warnings.append(f"BA number {ba_number_clean} not found in equipment records")
        else:
            validation.warnings.append("No BA number provided for fluid record")

        # Validate Fluid Type (required)
        if not fluid.fluid_type:
            validation.errors.append("Fluid type is required")

        # Validate numeric fields
        if fluid.capacity_ltrs is not None and fluid.capacity_ltrs < 0:
            validation.errors.append("Capacity cannot be negative")

        if fluid.current_stock_ltrs is not None and fluid.current_stock_ltrs < 0:
            validation.errors.append("Current stock cannot be negative")

        # Check if current stock exceeds capacity
        if (fluid.capacity_ltrs is not None and fluid.current_stock_ltrs is not None and
            fluid.current_stock_ltrs > fluid.capacity_ltrs):
            validation.warnings.append("Current stock exceeds capacity")

        validation.is_valid = len(validation.errors) == 0
        return validation

    def _process_maintenance_sheet(self, df: pd.DataFrame, sheet_name: str) -> List[MaintenancePreview]:
        """Process maintenance data from a DataFrame."""
        maintenance_records = []

        column_mapping = self._get_maintenance_column_mapping(df.columns)

        for index, row in df.iterrows():
            try:
                maintenance = MaintenancePreview(
                    row_index=index,
                    ba_number=self._extract_field(row, column_mapping.get('ba_number')),
                    maintenance_type=self._extract_field(row, column_mapping.get('maintenance_type')),
                    maintenance_category=self._extract_field(row, column_mapping.get('maintenance_category')),
                    done_date=self._extract_field(row, column_mapping.get('done_date')),
                    next_due_date=self._extract_field(row, column_mapping.get('next_due_date')),
                    raw_data=row.to_dict()
                )

                maintenance.validation = self._validate_maintenance_record(maintenance)
                maintenance_records.append(maintenance)

            except Exception as e:
                logger.error(f"Error processing maintenance row {index} in sheet {sheet_name}: {e}")
                error_maintenance = MaintenancePreview(
                    row_index=index,
                    ba_number=None,
                    maintenance_type=None,
                    raw_data=row.to_dict() if hasattr(row, 'to_dict') else {}
                )
                error_maintenance.validation = ValidationResult(
                    is_valid=False,
                    errors=[f"Error processing row: {str(e)}"]
                )
                maintenance_records.append(error_maintenance)

        return maintenance_records

    def _get_maintenance_column_mapping(self, columns: List[str]) -> Dict[str, str]:
        """Map Excel columns to maintenance fields."""
        mapping = {}
        columns_lower = {col.lower(): col for col in columns}

        # BA Number variations
        ba_variations = ['ba_number', 'ba number', 'ba_no', 'ba no', 'ba', 'asset_number']
        for variation in ba_variations:
            if variation in columns_lower:
                mapping['ba_number'] = columns_lower[variation]
                break

        # Maintenance Type variations
        type_variations = ['maintenance_type', 'maintenance type', 'type', 'service_type']
        for variation in type_variations:
            if variation in columns_lower:
                mapping['maintenance_type'] = columns_lower[variation]
                break

        # Maintenance Category variations
        category_variations = ['maintenance_category', 'maintenance category', 'category', 'tm_category']
        for variation in category_variations:
            if variation in columns_lower:
                mapping['maintenance_category'] = columns_lower[variation]
                break

        # Done Date variations
        done_variations = ['done_date', 'done date', 'completed_date', 'completion_date']
        for variation in done_variations:
            if variation in columns_lower:
                mapping['done_date'] = columns_lower[variation]
                break

        # Due Date variations
        due_variations = ['next_due_date', 'next due date', 'due_date', 'due date']
        for variation in due_variations:
            if variation in columns_lower:
                mapping['next_due_date'] = columns_lower[variation]
                break

        return mapping

    def _validate_maintenance_record(self, maintenance: MaintenancePreview) -> ValidationResult:
        """Validate a maintenance record."""
        validation = ValidationResult(is_valid=True)

        # Validate BA Number (for linking to equipment)
        if maintenance.ba_number:
            ba_number_clean = maintenance.ba_number.strip().upper()
            if ba_number_clean not in self.existing_ba_numbers:
                validation.warnings.append(f"BA number {ba_number_clean} not found in equipment records")
        else:
            validation.warnings.append("No BA number provided for maintenance record")

        # Validate Maintenance Type (required)
        if not maintenance.maintenance_type:
            validation.errors.append("Maintenance type is required")

        # Validate dates if provided
        if maintenance.done_date:
            try:
                utils.parse_date(maintenance.done_date)
            except:
                validation.errors.append("Invalid done date format")

        if maintenance.next_due_date:
            try:
                utils.parse_date(maintenance.next_due_date)
            except:
                validation.errors.append("Invalid due date format")

        validation.is_valid = len(validation.errors) == 0
        return validation

    def _validate_ba_numbers(self, preview_data: ImportPreviewData):
        """Validate BA numbers across all records and detect conflicts."""
        ba_numbers_in_import = set()

        # Check for duplicates within the import data
        for equipment in preview_data.equipment_records:
            if equipment.ba_number:
                ba_number_clean = equipment.ba_number.strip().upper()
                if ba_number_clean in ba_numbers_in_import:
                    preview_data.ba_number_conflicts.append(ba_number_clean)
                    equipment.validation.warnings.append("Duplicate BA number within import data")
                else:
                    ba_numbers_in_import.add(ba_number_clean)

    def _generate_validation_summary(self, preview_data: ImportPreviewData):
        """Generate validation summary statistics."""
        summary = {
            'total_records': 0,
            'valid_records': 0,
            'records_with_warnings': 0,
            'records_with_errors': 0,
            'ba_number_conflicts': len(preview_data.ba_number_conflicts),
            'missing_ba_numbers': 0,
            'duplicate_ba_numbers': 0
        }

        all_records = (preview_data.equipment_records +
                      preview_data.fluid_records +
                      preview_data.maintenance_records)

        for record in all_records:
            summary['total_records'] += 1

            if record.validation.is_valid:
                summary['valid_records'] += 1

            if record.validation.warnings:
                summary['records_with_warnings'] += 1

            if record.validation.errors:
                summary['records_with_errors'] += 1

            if hasattr(record, 'ba_number'):
                if not record.ba_number:
                    summary['missing_ba_numbers'] += 1
                elif record.validation.ba_number_status == 'duplicate':
                    summary['duplicate_ba_numbers'] += 1

        preview_data.validation_summary = summary
