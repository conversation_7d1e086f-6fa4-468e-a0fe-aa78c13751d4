"""
Conflict Resolution Manager for PROJECT-ALPHA Enhanced Excel Import

This manager handles the complete conflict resolution workflow for BA number
conflicts during Excel import. It coordinates between conflict detection,
user decisions, and final import processing.

Key Features:
- Sequential conflict resolution workflow
- Progress tracking through multiple conflicts
- User decision aggregation
- Integration with import preview system
- Modal dialog management in main GUI thread
"""

import logging
from typing import Dict, List, Optional, Set, Tuple
from PyQt5.QtWidgets import QWidget, QMessageBox, QProgressDialog
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

from enhanced_import_preview_engine import EquipmentPreview, ImportPreviewData
from ui.ba_conflict_dialog import BAConflictDialog
from models import Equipment
from database import get_db_connection

logger = logging.getLogger('conflict_resolution_manager')

class ConflictResolutionManager(QObject):
    """
    Manager for handling BA number conflict resolution workflow.
    
    Coordinates the entire conflict resolution process from detection
    through user decisions to final import processing.
    """
    
    # Signals for workflow coordination
    resolution_started = pyqtSignal(int)  # total_conflicts
    conflict_resolved = pyqtSignal(str, bool)  # ba_number, accepted
    resolution_completed = pyqtSignal(dict)  # final_decisions
    resolution_cancelled = pyqtSignal()
    
    def __init__(self, parent: QWidget = None):
        """
        Initialize the conflict resolution manager.
        
        Args:
            parent: Parent widget for modal dialogs
        """
        super().__init__(parent)
        self.parent_widget = parent
        self.conflicts_to_resolve = []  # List of (ba_number, existing_equipment, import_equipment)
        self.user_decisions = {}  # ba_number -> accept/reject decision
        self.current_conflict_index = 0
        self.current_dialog = None
        self.progress_dialog = None
        
    def start_conflict_resolution(self, preview_data: ImportPreviewData, 
                                 conflicting_ba_numbers: Set[str]) -> bool:
        """
        Start the conflict resolution workflow.
        
        Args:
            preview_data: Complete import preview data
            conflicting_ba_numbers: Set of BA numbers with conflicts
            
        Returns:
            True if conflicts were found and resolution started, False otherwise
        """
        if not conflicting_ba_numbers:
            logger.info("No BA number conflicts to resolve")
            return False
        
        # Prepare conflicts for resolution
        self.conflicts_to_resolve = []
        self.user_decisions = {}
        self.current_conflict_index = 0
        
        # Load existing equipment records for conflicting BA numbers
        existing_equipment = self._load_existing_equipment(conflicting_ba_numbers)
        
        # Build conflict list
        for equipment_preview in preview_data.equipment_records:
            if (equipment_preview.ba_number and 
                equipment_preview.ba_number.strip().upper() in conflicting_ba_numbers):
                
                ba_number = equipment_preview.ba_number.strip().upper()
                existing_record = existing_equipment.get(ba_number)
                
                if existing_record:
                    self.conflicts_to_resolve.append((ba_number, existing_record, equipment_preview))
        
        if not self.conflicts_to_resolve:
            logger.warning("No valid conflicts found for resolution")
            return False
        
        logger.info(f"Starting conflict resolution for {len(self.conflicts_to_resolve)} conflicts")
        
        # Show progress dialog
        self._show_progress_dialog()
        
        # Start resolution workflow
        self.resolution_started.emit(len(self.conflicts_to_resolve))
        self._resolve_next_conflict()
        
        return True
    
    def _load_existing_equipment(self, ba_numbers: Set[str]) -> Dict[str, Equipment]:
        """
        Load existing equipment records from database.
        
        Args:
            ba_numbers: Set of BA numbers to load
            
        Returns:
            Dictionary mapping BA numbers to Equipment objects
        """
        existing_equipment = {}
        
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Create placeholders for IN clause
            placeholders = ','.join(['?' for _ in ba_numbers])
            query = f"SELECT * FROM equipment WHERE UPPER(ba_number) IN ({placeholders})"
            
            cursor.execute(query, list(ba_numbers))
            rows = cursor.fetchall()
            
            # Convert rows to Equipment objects
            for row in rows:
                equipment = Equipment()
                equipment.id = row[0]
                equipment.ba_number = row[1]
                equipment.serial_number = row[2]
                equipment.make_and_type = row[3]
                equipment.units_held = row[4]
                equipment.vintage_years = row[5]
                equipment.meterage_kms = row[6]
                equipment.is_active = bool(row[7])
                
                ba_key = equipment.ba_number.strip().upper() if equipment.ba_number else ""
                if ba_key:
                    existing_equipment[ba_key] = equipment
            
            conn.close()
            logger.info(f"Loaded {len(existing_equipment)} existing equipment records")
            
        except Exception as e:
            logger.error(f"Error loading existing equipment: {e}")
        
        return existing_equipment
    
    def _show_progress_dialog(self):
        """Show progress dialog for conflict resolution."""
        self.progress_dialog = QProgressDialog(
            "Resolving BA number conflicts...",
            "Cancel",
            0,
            len(self.conflicts_to_resolve),
            self.parent_widget
        )
        self.progress_dialog.setWindowTitle("Import Conflict Resolution")
        self.progress_dialog.setModal(True)
        self.progress_dialog.canceled.connect(self._cancel_resolution)
        self.progress_dialog.show()
    
    def _resolve_next_conflict(self):
        """Resolve the next conflict in the queue."""
        if self.current_conflict_index >= len(self.conflicts_to_resolve):
            # All conflicts resolved
            self._complete_resolution()
            return
        
        # Update progress
        if self.progress_dialog:
            self.progress_dialog.setValue(self.current_conflict_index)
            self.progress_dialog.setLabelText(
                f"Resolving conflict {self.current_conflict_index + 1} of {len(self.conflicts_to_resolve)}"
            )
        
        # Get current conflict
        ba_number, existing_equipment, import_equipment = self.conflicts_to_resolve[self.current_conflict_index]
        
        # Show conflict resolution dialog
        self.current_dialog = BAConflictDialog(
            ba_number=ba_number,
            existing_equipment=existing_equipment,
            import_equipment=import_equipment,
            parent=self.parent_widget
        )
        
        # Connect dialog signals
        self.current_dialog.conflict_resolved.connect(self._on_conflict_resolved)
        
        # Show dialog
        self.current_dialog.show()
    
    def _on_conflict_resolved(self, ba_number: str, accept_import: bool):
        """
        Handle individual conflict resolution.
        
        Args:
            ba_number: The BA number that was resolved
            accept_import: True if user chose to accept import, False to skip
        """
        # Record user decision
        self.user_decisions[ba_number] = accept_import
        
        logger.info(f"Conflict resolved for {ba_number}: {'Accept' if accept_import else 'Skip'}")
        
        # Emit signal for this conflict
        self.conflict_resolved.emit(ba_number, accept_import)
        
        # Clean up current dialog
        if self.current_dialog:
            self.current_dialog.close()
            self.current_dialog = None
        
        # Move to next conflict
        self.current_conflict_index += 1
        
        # Use QTimer to avoid recursive dialog calls
        QTimer.singleShot(100, self._resolve_next_conflict)
    
    def _complete_resolution(self):
        """Complete the conflict resolution workflow."""
        # Close progress dialog
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        # Log resolution summary
        accepted_count = sum(1 for decision in self.user_decisions.values() if decision)
        skipped_count = len(self.user_decisions) - accepted_count
        
        logger.info(f"Conflict resolution completed: {accepted_count} accepted, {skipped_count} skipped")
        
        # Show completion message
        if self.parent_widget:
            QMessageBox.information(
                self.parent_widget,
                "Conflict Resolution Complete",
                f"Resolved {len(self.user_decisions)} BA number conflicts:\n"
                f"• {accepted_count} records will be imported (overwriting existing)\n"
                f"• {skipped_count} records will be skipped\n\n"
                f"Click 'Confirm Import' to proceed with the import."
            )
        
        # Emit completion signal
        self.resolution_completed.emit(self.user_decisions)
    
    def _cancel_resolution(self):
        """Cancel the conflict resolution workflow."""
        # Close any open dialogs
        if self.current_dialog:
            self.current_dialog.close()
            self.current_dialog = None
        
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        # Clear state
        self.conflicts_to_resolve = []
        self.user_decisions = {}
        self.current_conflict_index = 0
        
        logger.info("Conflict resolution cancelled by user")
        
        # Emit cancellation signal
        self.resolution_cancelled.emit()
    
    def get_resolution_decisions(self) -> Dict[str, bool]:
        """
        Get the final conflict resolution decisions.
        
        Returns:
            Dictionary mapping BA numbers to accept/reject decisions
        """
        return self.user_decisions.copy()
    
    def has_pending_conflicts(self) -> bool:
        """
        Check if there are pending conflicts to resolve.
        
        Returns:
            True if conflicts are pending, False otherwise
        """
        return (self.current_conflict_index < len(self.conflicts_to_resolve) or 
                self.current_dialog is not None)
    
    def get_conflict_summary(self) -> Dict[str, int]:
        """
        Get a summary of conflict resolution results.
        
        Returns:
            Dictionary with conflict statistics
        """
        if not self.user_decisions:
            return {'total': 0, 'accepted': 0, 'skipped': 0}
        
        accepted = sum(1 for decision in self.user_decisions.values() if decision)
        total = len(self.user_decisions)
        
        return {
            'total': total,
            'accepted': accepted,
            'skipped': total - accepted
        }
