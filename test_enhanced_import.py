"""
Test Script for Enhanced Excel Import System

This script provides basic testing for the enhanced Excel import components
to verify they work correctly before full integration testing.

Run this script to test:
1. Preview engine functionality
2. Dialog creation and display
3. Conflict detection logic
4. Controller workflow coordination
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QFileDialog, QMessageBox
from PyQt5.QtCore import Qt

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_enhanced_import')

class TestMainWindow(QMainWindow):
    """Simple test window for enhanced import system."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Enhanced Import System Test")
        self.setGeometry(100, 100, 400, 300)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        
        # Test buttons
        self.test_preview_btn = QPushButton("Test Preview Engine")
        self.test_preview_btn.clicked.connect(self.test_preview_engine)
        layout.addWidget(self.test_preview_btn)
        
        self.test_dialog_btn = QPushButton("Test Preview Dialog")
        self.test_dialog_btn.clicked.connect(self.test_preview_dialog)
        layout.addWidget(self.test_dialog_btn)
        
        self.test_conflict_btn = QPushButton("Test Conflict Dialog")
        self.test_conflict_btn.clicked.connect(self.test_conflict_dialog)
        layout.addWidget(self.test_conflict_btn)
        
        self.test_controller_btn = QPushButton("Test Full Controller")
        self.test_controller_btn.clicked.connect(self.test_full_controller)
        layout.addWidget(self.test_controller_btn)
        
        # Initialize controller
        self.import_controller = None
    
    def test_preview_engine(self):
        """Test the preview engine with a selected Excel file."""
        try:
            # Select Excel file
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Select Excel File for Preview Test", "", "Excel Files (*.xls *.xlsx)"
            )
            
            if not file_path:
                return
            
            logger.info(f"Testing preview engine with file: {file_path}")
            
            # Import and test preview engine
            from enhanced_import_preview_engine import EnhancedImportPreviewEngine
            
            engine = EnhancedImportPreviewEngine(file_path)
            preview_data = engine.generate_preview()
            
            if preview_data:
                # Show results
                equipment_count = len(preview_data.equipment_records)
                fluid_count = len(preview_data.fluid_records)
                maintenance_count = len(preview_data.maintenance_records)
                
                QMessageBox.information(
                    self,
                    "Preview Engine Test Results",
                    f"Preview data generated successfully!\n\n"
                    f"Equipment records: {equipment_count}\n"
                    f"Fluid records: {fluid_count}\n"
                    f"Maintenance records: {maintenance_count}\n\n"
                    f"File info: {preview_data.file_info.filename}\n"
                    f"Sheets processed: {len(preview_data.file_info.sheet_names)}"
                )
                
                logger.info(f"Preview engine test successful: {equipment_count} equipment, "
                           f"{fluid_count} fluids, {maintenance_count} maintenance")
            else:
                QMessageBox.warning(self, "Preview Engine Test", "Failed to generate preview data")
                logger.error("Preview engine test failed")
                
        except Exception as e:
            logger.error(f"Preview engine test error: {e}")
            QMessageBox.critical(self, "Preview Engine Test Error", f"Error testing preview engine:\n{str(e)}")
    
    def test_preview_dialog(self):
        """Test the preview dialog with sample data."""
        try:
            # First generate preview data
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Select Excel File for Dialog Test", "", "Excel Files (*.xls *.xlsx)"
            )
            
            if not file_path:
                return
            
            logger.info(f"Testing preview dialog with file: {file_path}")
            
            # Generate preview data
            from enhanced_import_preview_engine import EnhancedImportPreviewEngine
            
            engine = EnhancedImportPreviewEngine(file_path)
            preview_data = engine.generate_preview()
            
            if not preview_data:
                QMessageBox.warning(self, "Dialog Test", "Failed to generate preview data for dialog test")
                return
            
            # Import and show preview dialog
            from ui.import_preview_dialog import ImportPreviewDialog
            
            dialog = ImportPreviewDialog(preview_data, self)
            
            # Connect signals for testing
            dialog.import_confirmed.connect(self.on_test_import_confirmed)
            dialog.import_cancelled.connect(self.on_test_import_cancelled)
            
            # Show dialog
            dialog.show()
            
            logger.info("Preview dialog test started - dialog should be visible")
            
        except Exception as e:
            logger.error(f"Preview dialog test error: {e}")
            QMessageBox.critical(self, "Preview Dialog Test Error", f"Error testing preview dialog:\n{str(e)}")
    
    def test_conflict_dialog(self):
        """Test the conflict resolution dialog with sample data."""
        try:
            logger.info("Testing conflict resolution dialog")
            
            # Create sample data for conflict testing
            from enhanced_import_preview_engine import EquipmentPreview, ValidationResult
            from models import Equipment
            from ui.ba_conflict_dialog import BAConflictDialog
            
            # Create sample existing equipment
            existing_equipment = Equipment()
            existing_equipment.id = 1
            existing_equipment.ba_number = "12A345"
            existing_equipment.serial_number = "SN001"
            existing_equipment.make_and_type = "Existing Make/Type"
            existing_equipment.units_held = 2
            existing_equipment.vintage_years = 5.0
            existing_equipment.meterage_kms = 1000.0
            existing_equipment.is_active = True
            
            # Create sample import equipment
            import_equipment = EquipmentPreview()
            import_equipment.ba_number = "12A345"
            import_equipment.serial_number = "SN002"
            import_equipment.make_and_type = "New Make/Type"
            import_equipment.units_held = 3
            import_equipment.vintage_years = 3.0
            import_equipment.meterage_kms = 500.0
            import_equipment.is_active = True
            import_equipment.validation = ValidationResult()
            import_equipment.validation.ba_number_status = "duplicate"
            
            # Create and show conflict dialog
            conflict_dialog = BAConflictDialog("12A345", existing_equipment, import_equipment, self)
            
            # Connect signal for testing
            conflict_dialog.conflict_resolved.connect(self.on_test_conflict_resolved)
            
            # Show dialog
            conflict_dialog.show()
            
            logger.info("Conflict dialog test started - dialog should be visible")
            
        except Exception as e:
            logger.error(f"Conflict dialog test error: {e}")
            QMessageBox.critical(self, "Conflict Dialog Test Error", f"Error testing conflict dialog:\n{str(e)}")
    
    def test_full_controller(self):
        """Test the full enhanced import controller."""
        try:
            # Select Excel file
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Select Excel File for Full Controller Test", "", "Excel Files (*.xls *.xlsx)"
            )
            
            if not file_path:
                return
            
            logger.info(f"Testing full controller with file: {file_path}")
            
            # Import and create controller
            from enhanced_import_controller import EnhancedImportController
            
            if not self.import_controller:
                self.import_controller = EnhancedImportController(self)
                
                # Connect signals for testing
                self.import_controller.import_started.connect(self.on_controller_import_started)
                self.import_controller.preview_ready.connect(self.on_controller_preview_ready)
                self.import_controller.conflicts_detected.connect(self.on_controller_conflicts_detected)
                self.import_controller.import_completed.connect(self.on_controller_import_completed)
                self.import_controller.import_failed.connect(self.on_controller_import_failed)
                self.import_controller.import_cancelled.connect(self.on_controller_import_cancelled)
            
            # Start the enhanced import
            success = self.import_controller.start_enhanced_import(file_path)
            
            if success:
                logger.info("Full controller test started successfully")
            else:
                QMessageBox.warning(self, "Controller Test", "Failed to start enhanced import controller")
                logger.error("Full controller test failed to start")
                
        except Exception as e:
            logger.error(f"Full controller test error: {e}")
            QMessageBox.critical(self, "Controller Test Error", f"Error testing full controller:\n{str(e)}")
    
    # Test signal handlers
    def on_test_import_confirmed(self, import_data):
        """Handle test import confirmation."""
        equipment_count = len(import_data.get('equipment', []))
        fluid_count = len(import_data.get('fluids', []))
        maintenance_count = len(import_data.get('maintenance', []))
        
        QMessageBox.information(
            self,
            "Preview Dialog Test Result",
            f"Import confirmed with:\n"
            f"Equipment: {equipment_count}\n"
            f"Fluids: {fluid_count}\n"
            f"Maintenance: {maintenance_count}"
        )
        logger.info(f"Test import confirmed: {equipment_count} equipment, {fluid_count} fluids, {maintenance_count} maintenance")
    
    def on_test_import_cancelled(self):
        """Handle test import cancellation."""
        QMessageBox.information(self, "Preview Dialog Test Result", "Import cancelled by user")
        logger.info("Test import cancelled")
    
    def on_test_conflict_resolved(self, ba_number, accept_import):
        """Handle test conflict resolution."""
        action = "Accept" if accept_import else "Skip"
        QMessageBox.information(
            self,
            "Conflict Dialog Test Result",
            f"Conflict resolved for BA {ba_number}:\n{action} Import"
        )
        logger.info(f"Test conflict resolved: {ba_number} -> {action}")
    
    # Controller test signal handlers
    def on_controller_import_started(self, file_path):
        """Handle controller import started."""
        logger.info(f"Controller test: Import started for {file_path}")
    
    def on_controller_preview_ready(self, preview_data):
        """Handle controller preview ready."""
        logger.info("Controller test: Preview data ready")
    
    def on_controller_conflicts_detected(self, conflict_count):
        """Handle controller conflicts detected."""
        logger.info(f"Controller test: {conflict_count} conflicts detected")
    
    def on_controller_import_completed(self, import_results):
        """Handle controller import completed."""
        total_imported = sum(import_results.get(key, 0) for key in ['equipment_imported', 'fluids_imported', 'maintenance_imported'])
        QMessageBox.information(
            self,
            "Controller Test Complete",
            f"Enhanced import completed!\n"
            f"Total records imported: {total_imported}\n"
            f"Equipment: {import_results.get('equipment_imported', 0)}\n"
            f"Fluids: {import_results.get('fluids_imported', 0)}\n"
            f"Maintenance: {import_results.get('maintenance_imported', 0)}"
        )
        logger.info(f"Controller test completed: {import_results}")
    
    def on_controller_import_failed(self, error_message):
        """Handle controller import failed."""
        logger.error(f"Controller test failed: {error_message}")
    
    def on_controller_import_cancelled(self):
        """Handle controller import cancelled."""
        logger.info("Controller test cancelled")

def main():
    """Run the enhanced import system test."""
    app = QApplication(sys.argv)
    
    # Create and show test window
    window = TestMainWindow()
    window.show()
    
    logger.info("Enhanced import system test application started")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
